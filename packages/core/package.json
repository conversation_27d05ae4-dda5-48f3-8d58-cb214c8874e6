{"name": "@ckb-ccc/core", "version": "1.9.0", "description": "Core of CCC - CKBer's Codebase", "author": "Hanssen0 <<EMAIL>>", "license": "MIT", "private": false, "homepage": "https://github.com/ckb-devrel/ccc", "repository": {"type": "git", "url": "git://github.com/ckb-devrel/ccc.git"}, "sideEffects": false, "main": "./dist.commonjs/index.js", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist.commonjs/index.js", "default": "./dist.commonjs/index.js"}, "./barrel": {"import": "./dist/barrel.js", "require": "./dist.commonjs/barrel.js", "default": "./dist.commonjs/barrel.js"}, "./advancedBarrel": {"import": "./dist/advancedBarrel.js", "require": "./dist.commonjs/advancedBarrel.js", "default": "./dist.commonjs/advancedBarrel.js"}, "./advanced": {"import": "./dist/advanced.js", "require": "./dist.commonjs/advanced.js", "default": "./dist.commonjs/advanced.js"}}, "scripts": {"test": "vitest", "test:ci": "vitest run", "build": "rimraf ./dist && rimraf ./dist.commonjs && tsc && tsc --project tsconfig.commonjs.json && copyfiles -u 2 misc/basedirs/**/* .", "lint": "eslint ./src", "format": "prettier --write . && eslint --fix ./src"}, "devDependencies": {"@eslint/js": "^9.1.1", "@types/blake2b": "^2.1.3", "@types/ws": "^8.5.12", "copyfiles": "^2.4.1", "eslint": "^9.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "rimraf": "^5.0.5", "ts-essentials": "^9.4.2", "typescript": "^5.4.5", "typescript-eslint": "^7.7.0", "vitest": "^3.2.2"}, "publishConfig": {"access": "public"}, "dependencies": {"@joyid/ckb": "^1.1.1", "@noble/ciphers": "^0.5.3", "@noble/curves": "^1.4.2", "@noble/hashes": "^1.4.0", "abort-controller": "^3.0.0", "bech32": "^2.0.0", "bitcoinjs-message": "^2.2.0", "bs58check": "^4.0.0", "buffer": "^6.0.3", "cross-fetch": "^4.0.0", "ethers": "^6.13.1", "isomorphic-ws": "^5.0.0", "ws": "^8.18.0"}, "packageManager": "pnpm@10.8.1"}