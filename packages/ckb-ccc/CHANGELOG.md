# ckb-ccc

## 1.0.18
### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.1.10

## 1.0.17

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.1.9

## 1.0.16

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.1.8

## 1.0.11

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.1.3

## 1.0.10

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.1.2

## 1.0.9

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.1.1

## 1.0.8

### Patch Changes

- Updated dependencies [[`8c97c85`](https://github.com/ckb-devrel/ccc/commit/8c97c851db4a2d940c7e59116ca7620cfd0afae1)]:
  - @ckb-ccc/ccc@1.1.0

## 1.0.7

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.7

## 1.0.6

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.6

## 1.0.5

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.5

## 1.0.4

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.4

## 1.0.3

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.3

## 1.0.2

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.2

## 1.0.1

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@1.0.1

## 1.0.0

### Major Changes

- [#107](https://github.com/ckb-devrel/ccc/pull/107) [`b99f55f`](https://github.com/ckb-devrel/ccc/commit/b99f55f74e64106391ce53f7d0bd0fa7522023cc) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: molecule

### Patch Changes

- Updated dependencies [[`b99f55f`](https://github.com/ckb-devrel/ccc/commit/b99f55f74e64106391ce53f7d0bd0fa7522023cc)]:
  - @ckb-ccc/ccc@1.0.0

## 0.0.18

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.18

## 0.0.17

### Patch Changes

- Updated dependencies [[`d9affcc`](https://github.com/ckb-devrel/ccc/commit/d9affcc01c7b839b227e4d79bcb66e717577502a)]:
  - @ckb-ccc/ccc@0.0.17

## 0.0.16

### Patch Changes

- [#69](https://github.com/ckb-devrel/ccc/pull/69) [`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Client.waitTransaction

- [#70](https://github.com/ckb-devrel/ccc/pull/70) [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): websocket transport

- [#67](https://github.com/ckb-devrel/ccc/pull/67) [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): auto fee rate

- [#60](https://github.com/ckb-devrel/ccc/pull/60) [`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: support Xverse

- Updated dependencies [[`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a), [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366), [`e63a06e`](https://github.com/ckb-devrel/ccc/commit/e63a06ee75ac8595208d216dec88a4228c465e23), [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f), [`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34)]:
  - @ckb-ccc/ccc@0.0.16

## 0.0.16-alpha.9

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.9

## 0.0.16-alpha.8

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.8

## 0.0.16-alpha.7

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.7

## 0.0.16-alpha.6

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.6

## 0.0.16-alpha.5

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.5

## 0.0.16-alpha.4

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.4

## 0.0.16-alpha.3

### Patch Changes

- [#70](https://github.com/ckb-devrel/ccc/pull/70) [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): websocket transport

- Updated dependencies [[`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366)]:
  - @ckb-ccc/ccc@0.0.16-alpha.3

## 0.0.16-alpha.2

### Patch Changes

- [#69](https://github.com/ckb-devrel/ccc/pull/69) [`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Client.waitTransaction

- [#67](https://github.com/ckb-devrel/ccc/pull/67) [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): auto fee rate

- Updated dependencies [[`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a), [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f)]:
  - @ckb-ccc/ccc@0.0.16-alpha.2

## 0.0.16-alpha.1

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.16-alpha.1

## 0.0.16-alpha.0

### Patch Changes

- [#60](https://github.com/ckb-devrel/ccc/pull/60) [`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: support Xverse

- Updated dependencies [[`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34)]:
  - @ckb-ccc/ccc@0.0.16-alpha.0

## 0.0.15

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.15

## 0.0.14

### Patch Changes

- [#56](https://github.com/ckb-devrel/ccc/pull/56) [`f13f4d3`](https://github.com/ckb-devrel/ccc/commit/f13f4d319ca66b571029a65e945e3a038bfeea25) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findTransactions

- [#48](https://github.com/ckb-devrel/ccc/pull/48) [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(okx): make okx wallet happy

- Updated dependencies [[`f13f4d3`](https://github.com/ckb-devrel/ccc/commit/f13f4d319ca66b571029a65e945e3a038bfeea25), [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6)]:
  - @ckb-ccc/ccc@0.0.14

## 0.0.14-alpha.2

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.14-alpha.2

## 0.0.14-alpha.1

### Patch Changes

- [#48](https://github.com/ckb-devrel/ccc/pull/48) [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(okx): make okx wallet happy

- Updated dependencies [[`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6)]:
  - @ckb-ccc/ccc@0.0.14-alpha.1

## 0.0.14-alpha.0

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.14-alpha.0

## 0.0.13

### Patch Changes

- [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): recordCells should not add usableCells

- [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): node.js websocket

- [#25](https://github.com/ckb-devrel/ccc/pull/25) [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(utxo-global): switchNetwork

- [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): completeInputs

- [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): more APIs. Since parsing.

- [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findCells

- Updated dependencies [[`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97), [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f), [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97), [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b), [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11), [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895)]:
  - @ckb-ccc/ccc@0.0.13

## 0.0.13-alpha.8

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.13-alpha.8

## 0.0.13-alpha.7

### Patch Changes

- [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): more APIs. Since parsing.

- [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findCells

- Updated dependencies [[`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11), [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895)]:
  - @ckb-ccc/ccc@0.0.13-alpha.7

## 0.0.13-alpha.6

### Patch Changes

- [#25](https://github.com/ckb-devrel/ccc/pull/25) [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(utxo-global): switchNetwork

- [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): completeInputs

- Updated dependencies [[`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97), [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b)]:
  - @ckb-ccc/ccc@0.0.13-alpha.6

## 0.0.13-alpha.5

### Patch Changes

- [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): recordCells should not add usableCells

- Updated dependencies [[`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97)]:
  - @ckb-ccc/ccc@0.0.13-alpha.5

## 0.0.13-alpha.4

### Patch Changes

- [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): node.js websocket

- Updated dependencies [[`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f)]:
  - @ckb-ccc/ccc@0.0.13-alpha.4

## 0.0.13-alpha.3

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.13-alpha.3

## 0.0.13-alpha.2

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.13-alpha.2

## 0.0.13-alpha.1

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.13-alpha.1

## 0.0.13-alpha.0

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/ccc@0.0.13-alpha.0

## 0.0.12

### Patch Changes

- Updated dependencies [[`591e779`](https://github.com/ckb-devrel/ccc/commit/591e7794ce3d07ceaad55b7a80d2277fe0aa9fe7)]:
  - @ckb-ccc/ccc@0.0.12

## 0.0.12-alpha.7

### Patch Changes

- @ckb-ccc/ccc@0.0.12-alpha.7
