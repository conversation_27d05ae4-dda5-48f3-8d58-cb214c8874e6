import { encodeSvgToImgSrc } from "../utils.js";

export const ETH_SVG = encodeSvgToImgSrc(
  '<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_46_558)"><circle cx="16" cy="16" r="16" fill="#627EEA"/><path d="M15.9957 20.4203L9.14282 16.3876L15.9957 5.14258V20.4203Z" fill="white"/><path d="M15.9957 26.8573L9.30029 17.396L15.9957 21.3511V26.8573Z" fill="white"/><path d="M16.2319 20.4203L23.0849 16.3876L16.2319 5.14258V20.4203Z" fill="#C1CCF7"/><path d="M16.3107 26.8573L23.0848 17.396L16.3107 21.3511V26.8573Z" fill="#C1CCF7"/></g><defs><clipPath id="clip0_46_558"><rect width="32" height="32" fill="white"/></clipPath></defs></svg>',
);
