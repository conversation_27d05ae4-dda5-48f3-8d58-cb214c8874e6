import { encodeSvgToImgSrc } from "../utils.js";

export const CKB_SVG = encodeSvgToImgSrc(
  '<svg width="668" height="666.66669" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg"><polygon points="170.265,206.318 223.416,206.318 223.416,93.897 264.161,93.897 170.265,0 " transform="matrix(1.8256046,0,0,1.8256046,-166.4037,144.47964)" /><polygon points="324.79,112.422 284.046,112.422 377.941,206.318 377.941,0 324.79,0 " transform="matrix(1.8256046,0,0,1.8256046,-166.4037,144.47964)" /></svg>',
);
