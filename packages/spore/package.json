{"name": "@ckb-ccc/spore", "version": "1.5.2", "description": "CCC - CKBer's Codebase. Common Chains Connector's support for Spore protocol", "author": "ashuralyk <<EMAIL>>", "license": "MIT", "private": false, "homepage": "https://github.com/ckb-devrel/ccc", "repository": {"type": "git", "url": "git://github.com/ckb-devrel/ccc.git"}, "sideEffects": false, "main": "dist.commonjs/index.js", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist.commonjs/index.js", "default": "./dist.commonjs/index.js"}, "./barrel": {"import": "./dist/barrel.js", "require": "./dist.commonjs/barrel.js", "default": "./dist.commonjs/barrel.js"}, "./advanced": {"import": "./dist/advanced.js", "require": "./dist.commonjs/advanced.js", "default": "./dist.commonjs/advanced.js"}, "./advancedBarrel": {"import": "./dist/advancedBarrel.js", "require": "./dist.commonjs/advancedBarrel.js", "default": "./dist.commonjs/advancedBarrel.js"}}, "scripts": {"build": "rimraf ./dist && rimraf ./dist.commonjs && tsc && tsc --project tsconfig.commonjs.json && copyfiles -u 2 misc/basedirs/**/* .", "lint": "eslint ./src", "format": "prettier --write . && eslint --fix ./src"}, "devDependencies": {"@eslint/js": "^9.1.1", "@types/node": "^22.10.0", "copyfiles": "^2.4.1", "dotenv": "^16.4.5", "eslint": "^9.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "rimraf": "^5.0.5", "typescript": "^5.4.5", "typescript-eslint": "^7.7.0", "vitest": "^3.2.2"}, "publishConfig": {"access": "public"}, "dependencies": {"@ckb-ccc/core": "workspace:*", "axios": "^1.7.7"}, "packageManager": "pnpm@10.8.1"}