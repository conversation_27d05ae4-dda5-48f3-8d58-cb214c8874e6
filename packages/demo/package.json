{"name": "@ckb-ccc/ccc-demo", "version": "0.0.1", "private": true, "type": "module", "homepage": "https://github.com/ckb-devrel/ccc", "repository": {"type": "git", "url": "git://github.com/ckb-devrel/ccc.git"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@lit/react": "^1.0.5", "@next/third-parties": "^15.3.4", "@uiw/react-json-view": "2.0.0-alpha.30", "lucide-react": "^0.427.0", "next": "15.3.3", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@ckb-ccc/connector-react": "workspace:*", "@ckb-ccc/lumos-patches": "workspace:*", "@ckb-ccc/ssri": "workspace:*", "@ckb-ccc/udt": "workspace:*", "@ckb-lumos/ckb-indexer": "^0.24.0-next.1", "@ckb-lumos/common-scripts": "^0.24.0-next.1", "@ckb-lumos/config-manager": "^0.24.0-next.1", "@ckb-lumos/helpers": "^0.24.0-next.1", "@headlessui/react": "^1.7.19", "@heroicons/react": "^2.1.3", "@scure/bip32": "^1.4.0", "@scure/bip39": "^1.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "pnpm@10.8.1"}