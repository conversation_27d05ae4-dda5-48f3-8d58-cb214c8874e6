// prettier.config.js, .prettierrc.js, prettier.config.mjs, or .prettierrc.mjs

/**
 * @see https://prettier.io/docs/configuration
 * @type {import("prettier").Config}
 */
const config = {
  singleQuote: false,
  trailingComma: "all",
  plugins: [
    import.meta.resolve("prettier-plugin-organize-imports/index.js"),
    import.meta.resolve("prettier-plugin-tailwindcss"),
  ],
};

export default config;
