<p align="center">
  <a href="https://live.ckbccc.com/">
    <img alt="Logo" src="https://raw.githubusercontent.com/ckb-devrel/ccc/master/assets/logoAndText.svg" style="height: 8rem; max-width: 90%; padding: 0.5rem 0;" />
  </a>
</p>

<h1 align="center" style="font-size: 48px;">
  CCC Playground
</h1>

<p align="center">
  <img alt="GitHub commit activity" src="https://img.shields.io/github/commit-activity/m/ckb-devrel/ccc" />
  <img alt="GitHub last commit" src="https://img.shields.io/github/last-commit/ckb-devrel/ccc/master" />
  <a href="https://live.ckbccc.com/"><img
    alt="Playground" src="https://img.shields.io/website?url=https%3A%2F%2Flive.ckbccc.com%2F&label=Playground"
  /></a>
  <a href="https://app.ckbccc.com/"><img
    alt="App" src="https://img.shields.io/website?url=https%3A%2F%2Fapp.ckbccc.com%2F&label=App"
  /></a>
  <a href="https://docs.ckbccc.com/"><img
    alt="Docs" src="https://img.shields.io/website?url=https%3A%2F%2Fdocs.ckbccc.com%2F&label=Docs"
  /></a>
</p>

<p align="center">
  "CCC - CKBers' Codebase" is the next step of "Common Chains Connector".
  <br />
  Empower yourself with CCC to discover the unlimited potential of CKB.
  <br />
  Interoperate with wallets from different chain ecosystems.
  <br />
  Fully enabling CKB's Turing completeness and cryptographic freedom power.
  <br />
  Start exploring CKB from CCC playground!
</p>

## Preview

<p align="center">
  <a href="https://live.ckbccc.com/">
    <img src="https://raw.githubusercontent.com/ckb-devrel/ccc/master/packages/playground/assets/preview.png" width="60%" />
  </a>
</p>

This project is still under active development, and we are looking forward to your feedback. [Try it now here](https://live.ckbccc.com/).

## Documents

- Check [the repo of project CCC](https://github.com/ckb-devrel/ccc) for detailed introduction.
- [Documents site](https://docs.ckbccc.com).
- [API reference](https://api.ckbccc.com).

## Run Locally

```shell
# Navigate to the project directory
pnpm install
pnpm build
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
