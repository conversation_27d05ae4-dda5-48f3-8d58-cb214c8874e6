/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #52B9E1;
  --ifm-color-primary-dark: #48a2c5;
  --ifm-color-primary-darker: #3e8aa9;
  --ifm-color-primary-darkest: #327088;
  --ifm-color-primary-light: #5bcefa;
  --ifm-color-primary-lighter: #74d5fc;
  --ifm-color-primary-lightest: #8bdeff;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #5bcefa;
  --ifm-color-primary-dark: #52B9E1;
  --ifm-color-primary-darker: #48a2c5;
  --ifm-color-primary-darkest: #3e8aa9;
  --ifm-color-primary-light: #74d5fc;
  --ifm-color-primary-lighter: #8bdeff;
  --ifm-color-primary-lightest: #9be0fb;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}
