{"name": "@ckb-ccc/connector-react", "version": "1.0.18", "description": "CCC - CKBer's Codebase. Common Chains Connector UI Component for React", "author": "Hanssen0 <<EMAIL>>", "license": "MIT", "private": false, "homepage": "https://github.com/ckb-devrel/ccc", "repository": {"type": "git", "url": "git://github.com/ckb-devrel/ccc.git"}, "type": "module", "main": "dist/index.js", "exports": {".": "./dist/index.js", "./barrel": "./dist/barrel.js", "./advancedBarrel": "./dist/advancedBarrel.js", "./advanced": "./dist/advanced.js"}, "scripts": {"build": "rimraf ./dist && tsc", "lint": "eslint ./src", "format": "prettier --write . && eslint --fix ./src"}, "devDependencies": {"@eslint/js": "^9.1.1", "@types/react": "^18", "eslint": "^9.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "rimraf": "^5.0.5", "typescript": "^5.4.5", "typescript-eslint": "^7.7.0"}, "publishConfig": {"access": "public"}, "dependencies": {"@ckb-ccc/connector": "workspace:*", "@lit/react": "^1.0.5"}, "peerDependencies": {"react": ">=16"}, "packageManager": "pnpm@10.8.1"}