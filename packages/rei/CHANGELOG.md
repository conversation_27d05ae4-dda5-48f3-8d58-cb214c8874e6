# @ckb-ccc/rei

## 1.0.18
### Patch Changes

- Updated dependencies [[`0f3aa3f`](https://github.com/ckb-devrel/ccc/commit/0f3aa3fe7798826e57fb8092a679320fb4dfc140), [`34fc83d`](https://github.com/ckb-devrel/ccc/commit/34fc83d316a99889f3019d8069c478113506fe7a), [`77865cd`](https://github.com/ckb-devrel/ccc/commit/77865cd2953e5e01d6dc610823ad3eb13e128902), [`46f1760`](https://github.com/ckb-devrel/ccc/commit/46f1760cdd5d6cf3d843e9fe8682f9cd4f31930d), [`2da4dc5`](https://github.com/ckb-devrel/ccc/commit/2da4dc5b5637b307c8010ccc22ef3f79c7dcca83)]:
  - @ckb-ccc/core@1.9.0

## 1.0.17

### Patch Changes

- Updated dependencies [[`2e37ad7`](https://github.com/ckb-devrel/ccc/commit/2e37ad72fb98f3d7dc059299dafc9bba84dcb846)]:
  - @ckb-ccc/core@1.8.1

## 1.0.16

### Patch Changes

- [#155](https://github.com/ckb-devrel/ccc/pull/155) [`7bed665`](https://github.com/ckb-devrel/ccc/commit/7bed665f6c9444b37893f1c6c8c3cbca344a6ca4) Thanks [@wendychaung](https://github.com/wendychaung)! - fix: update window.rei.ckb

- Updated dependencies [[`80e605d`](https://github.com/ckb-devrel/ccc/commit/80e605d0645e87b4e8b5be85c63322f7a3926e38), [`8c7e000`](https://github.com/ckb-devrel/ccc/commit/8c7e00069a276ac58afa4737623e95656d4852c5), [`732ad59`](https://github.com/ckb-devrel/ccc/commit/732ad59f13ea2cd47003033e30b310b8ff26f058), [`90b6e9f`](https://github.com/ckb-devrel/ccc/commit/90b6e9fee543b6ee16b96e27d6f86ff33fc57029), [`815fb4d`](https://github.com/ckb-devrel/ccc/commit/815fb4da3432b889b848eb70943d725988fe611d), [`def62a0`](https://github.com/ckb-devrel/ccc/commit/def62a08bf908c6a21fe91c8db2c60848a2ada52), [`d584059`](https://github.com/ckb-devrel/ccc/commit/d584059644e8bcd3a0ea8b0314fdcbb68ee66013), [`f58d398`](https://github.com/ckb-devrel/ccc/commit/f58d3980f08da1f3fa19cee45aa50c8b293294ea), [`1e88ad8`](https://github.com/ckb-devrel/ccc/commit/1e88ad8743428b46b28fe790bd559b96df8a6ce4), [`6f10589`](https://github.com/ckb-devrel/ccc/commit/6f1058977e7aa113808fa74793f1ad5d672626d2), [`601a729`](https://github.com/ckb-devrel/ccc/commit/601a7291e877b39c4032c95fab421ed3d41404c2), [`074b4cd`](https://github.com/ckb-devrel/ccc/commit/074b4cd3b0cdc925dc9ef99e8146564a60646f1e), [`d7728d9`](https://github.com/ckb-devrel/ccc/commit/d7728d9edb46c9c5a2bfeb342fc68a8b1c0fec5d), [`ab195a0`](https://github.com/ckb-devrel/ccc/commit/ab195a024aeee5e21ed19d89c2cf1bf8d52bb380), [`29a2e22`](https://github.com/ckb-devrel/ccc/commit/29a2e223b902ed23523e4948ab3fca793f9e5b01)]:
  - @ckb-ccc/core@1.8.0

## 1.0.11

### Patch Changes

- Updated dependencies [[`28c211d`](https://github.com/ckb-devrel/ccc/commit/28c211d839a2d2305eca56e82ba7da144aa3df4a)]:
  - @ckb-ccc/core@1.5.0

## 1.0.10

### Patch Changes

- Updated dependencies [[`3fdb2c4`](https://github.com/ckb-devrel/ccc/commit/3fdb2c477d0b2766b231e436b8f396f047b02634), [`f429087`](https://github.com/ckb-devrel/ccc/commit/f4290874dfab3fe58844e5169673c5d47bda64e3)]:
  - @ckb-ccc/core@1.4.0

## 1.0.9

### Patch Changes

- Updated dependencies [[`a48cccf`](https://github.com/ckb-devrel/ccc/commit/a48cccfae3ce6b3456a5eb863f207e7e5a6e568f)]:
  - @ckb-ccc/core@1.3.1

## 1.0.8

### Patch Changes

- Updated dependencies [[`4dbf4fd`](https://github.com/ckb-devrel/ccc/commit/4dbf4fd8021cf14d05282706a7667ea7d108fb09), [`4c76f9e`](https://github.com/ckb-devrel/ccc/commit/4c76f9e2a93a226fcfc4c32a5378bb531bfff08f), [`c2c4c26`](https://github.com/ckb-devrel/ccc/commit/c2c4c264e04461948e4b913b2f22054e6032ddc8), [`b6a73fa`](https://github.com/ckb-devrel/ccc/commit/b6a73fa9628ebdff51cb8f246309654cd53e36f2), [`8c97c85`](https://github.com/ckb-devrel/ccc/commit/8c97c851db4a2d940c7e59116ca7620cfd0afae1)]:
  - @ckb-ccc/core@1.3.0

## 1.0.7

### Patch Changes

- Updated dependencies [[`01263bd`](https://github.com/ckb-devrel/ccc/commit/01263bd8c601fa8fcdfa24be52601716e1864843)]:
  - @ckb-ccc/core@1.2.3

## 1.0.6

### Patch Changes

- Updated dependencies [[`7886e3d`](https://github.com/ckb-devrel/ccc/commit/7886e3d89e9ca8f3514a2044c6dd4e8ec6b49933)]:
  - @ckb-ccc/core@1.2.2

## 1.0.5

### Patch Changes

- Updated dependencies [[`94e2618`](https://github.com/ckb-devrel/ccc/commit/94e26182515e09d6086ec5b653d091f117a499e6)]:
  - @ckb-ccc/core@1.2.1

## 1.0.4

### Patch Changes

- Updated dependencies [[`128e87b`](https://github.com/ckb-devrel/ccc/commit/128e87b5ca3e97bfe7842e76f786aa6aec010797)]:
  - @ckb-ccc/core@1.2.0

## 1.0.3

### Patch Changes

- Updated dependencies [[`925991c`](https://github.com/ckb-devrel/ccc/commit/925991c8a24b1f34667e30b28b69812e936e3928)]:
  - @ckb-ccc/core@1.1.1

## 1.0.2

### Patch Changes

- Updated dependencies [[`ddc0a28`](https://github.com/ckb-devrel/ccc/commit/ddc0a281c3d1dfa6ebc990dae92994f026dfddcc)]:
  - @ckb-ccc/core@1.1.0

## 1.0.1

### Patch Changes

- Updated dependencies [[`719055b`](https://github.com/ckb-devrel/ccc/commit/719055b404f31b40362f51714b9f11c85b857581), [`94caaca`](https://github.com/ckb-devrel/ccc/commit/94caaca11c63752a25282d42f51161c94397dec6)]:
  - @ckb-ccc/core@1.0.1

## 1.0.0

### Major Changes

- [#107](https://github.com/ckb-devrel/ccc/pull/107) [`b99f55f`](https://github.com/ckb-devrel/ccc/commit/b99f55f74e64106391ce53f7d0bd0fa7522023cc) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: molecule

### Patch Changes

- Updated dependencies [[`b99f55f`](https://github.com/ckb-devrel/ccc/commit/b99f55f74e64106391ce53f7d0bd0fa7522023cc)]:
  - @ckb-ccc/core@1.0.0

## 0.0.18

### Patch Changes

- Updated dependencies [[`c1cb910`](https://github.com/ckb-devrel/ccc/commit/c1cb91091780c7b33fbbd683ef8edc9f11452ecd)]:
  - @ckb-ccc/core@0.1.2

## 0.0.17

### Patch Changes

- [#101](https://github.com/ckb-devrel/ccc/pull/101) [`d9affcc`](https://github.com/ckb-devrel/ccc/commit/d9affcc01c7b839b227e4d79bcb66e717577502a) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix: commonjs

- Updated dependencies [[`d9affcc`](https://github.com/ckb-devrel/ccc/commit/d9affcc01c7b839b227e4d79bcb66e717577502a)]:
  - @ckb-ccc/core@0.1.1

## 0.0.16

### Patch Changes

- [#70](https://github.com/ckb-devrel/ccc/pull/70) [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): websocket transport

- Updated dependencies [[`a3d5359`](https://github.com/ckb-devrel/ccc/commit/a3d53595f6dd11f2f59cdf0086b3d7ce558a2fdd), [`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a), [`f07a506`](https://github.com/ckb-devrel/ccc/commit/f07a506bd6fc27fe659a17d2f7baaeec54716d81), [`f21d7e4`](https://github.com/ckb-devrel/ccc/commit/f21d7e4cf422edab4a836ef6d678b620594fef8d), [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366), [`1720d5a`](https://github.com/ckb-devrel/ccc/commit/1720d5a398543f1c6e24763eeaf15d84cd2214bf), [`e63a06e`](https://github.com/ckb-devrel/ccc/commit/e63a06ee75ac8595208d216dec88a4228c465e23), [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f), [`50f2ce0`](https://github.com/ckb-devrel/ccc/commit/50f2ce08e74cb3fbeae926267d42e28b426fd7f4), [`4709384`](https://github.com/ckb-devrel/ccc/commit/4709384e37188991cb937b16f99f47ca82c912b8)]:
  - @ckb-ccc/core@0.1.0

## 0.0.16-alpha.7

### Patch Changes

- Updated dependencies [[`f07a506`](https://github.com/ckb-devrel/ccc/commit/f07a506bd6fc27fe659a17d2f7baaeec54716d81)]:
  - @ckb-ccc/core@0.1.0-alpha.7

## 0.0.16-alpha.6

### Patch Changes

- Updated dependencies [[`4709384`](https://github.com/ckb-devrel/ccc/commit/4709384e37188991cb937b16f99f47ca82c912b8)]:
  - @ckb-ccc/core@0.1.0-alpha.6

## 0.0.16-alpha.5

### Patch Changes

- Updated dependencies [[`50f2ce0`](https://github.com/ckb-devrel/ccc/commit/50f2ce08e74cb3fbeae926267d42e28b426fd7f4)]:
  - @ckb-ccc/core@0.1.0-alpha.5

## 0.0.16-alpha.4

### Patch Changes

- Updated dependencies [[`f21d7e4`](https://github.com/ckb-devrel/ccc/commit/f21d7e4cf422edab4a836ef6d678b620594fef8d)]:
  - @ckb-ccc/core@0.1.0-alpha.4

## 0.0.16-alpha.3

### Patch Changes

- Updated dependencies [[`a3d5359`](https://github.com/ckb-devrel/ccc/commit/a3d53595f6dd11f2f59cdf0086b3d7ce558a2fdd)]:
  - @ckb-ccc/core@0.0.16-alpha.3

## 0.0.16-alpha.2

### Patch Changes

- [#70](https://github.com/ckb-devrel/ccc/pull/70) [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): websocket transport

- Updated dependencies [[`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366)]:
  - @ckb-ccc/core@0.0.16-alpha.2

## 0.0.16-alpha.1

### Patch Changes

- Updated dependencies [[`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a), [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f)]:
  - @ckb-ccc/core@0.0.16-alpha.1

## 0.0.16-alpha.0

### Patch Changes

- Updated dependencies [[`1720d5a`](https://github.com/ckb-devrel/ccc/commit/1720d5a398543f1c6e24763eeaf15d84cd2214bf)]:
  - @ckb-ccc/core@0.0.16-alpha.0

## 0.0.15

### Patch Changes

- Updated dependencies [[`8f2560a`](https://github.com/ckb-devrel/ccc/commit/8f2560ab0e5619654fff7c5eacda8425385f908e)]:
  - @ckb-ccc/core@0.0.15

## 0.0.14

### Patch Changes

- Updated dependencies [[`5e942f8`](https://github.com/ckb-devrel/ccc/commit/5e942f8f1ed678abdb7ab9716f5449f0714cea53), [`f5b5938`](https://github.com/ckb-devrel/ccc/commit/f5b5938ab8f9c0a338dfd6765fe717f7ad1b1dd8), [`f13f4d3`](https://github.com/ckb-devrel/ccc/commit/f13f4d319ca66b571029a65e945e3a038bfeea25), [`7ba62a0`](https://github.com/ckb-devrel/ccc/commit/7ba62a056f17808fe5684786c00c2dff80bb7d1d), [`2164efd`](https://github.com/ckb-devrel/ccc/commit/2164efd6d834c1917ad5f4a514dc25941f937185), [`e5bd2ad`](https://github.com/ckb-devrel/ccc/commit/e5bd2ad5de4b42a22c422ecfc42056750f69b88b), [`aae3e06`](https://github.com/ckb-devrel/ccc/commit/aae3e0679fb940dd8c12ac9be12a4b53277a339d)]:
  - @ckb-ccc/core@0.0.14

## 0.0.14-alpha.2

### Patch Changes

- Updated dependencies [[`7ba62a0`](https://github.com/ckb-devrel/ccc/commit/7ba62a056f17808fe5684786c00c2dff80bb7d1d)]:
  - @ckb-ccc/core@0.0.14-alpha.2

## 0.0.14-alpha.1

### Patch Changes

- Updated dependencies [[`5e942f8`](https://github.com/ckb-devrel/ccc/commit/5e942f8f1ed678abdb7ab9716f5449f0714cea53), [`e5bd2ad`](https://github.com/ckb-devrel/ccc/commit/e5bd2ad5de4b42a22c422ecfc42056750f69b88b), [`aae3e06`](https://github.com/ckb-devrel/ccc/commit/aae3e0679fb940dd8c12ac9be12a4b53277a339d)]:
  - @ckb-ccc/core@0.0.14-alpha.1

## 0.0.14-alpha.0

### Patch Changes

- Updated dependencies [[`f5b5938`](https://github.com/ckb-devrel/ccc/commit/f5b5938ab8f9c0a338dfd6765fe717f7ad1b1dd8), [`2164efd`](https://github.com/ckb-devrel/ccc/commit/2164efd6d834c1917ad5f4a514dc25941f937185)]:
  - @ckb-ccc/core@0.0.14-alpha.0

## 0.0.13

### Patch Changes

- [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): recordCells should not add usableCells

- [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): node.js websocket

- [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): completeInputs

- [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): more APIs. Since parsing.

- [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findCells

- Updated dependencies [[`3378e85`](https://github.com/ckb-devrel/ccc/commit/3378e85b32797f5cdc1943b9ecaca1fd1d9fad5e), [`8629449`](https://github.com/ckb-devrel/ccc/commit/86294490e76fc2a1cee20f827883e02fceca6e8b), [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97), [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f), [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97), [`600cc13`](https://github.com/ckb-devrel/ccc/commit/600cc137ac6eb7c5b2533670de6df29d82f1b9e1), [`642f731`](https://github.com/ckb-devrel/ccc/commit/642f7317f4951ef801f1245aea96c40b4b6fb73e), [`96dbb61`](https://github.com/ckb-devrel/ccc/commit/96dbb6107d2071b9383350ddd578557746227054), [`0462a4e`](https://github.com/ckb-devrel/ccc/commit/0462a4ee101926f0da857173626dc4ab879e3b56), [`52156f9`](https://github.com/ckb-devrel/ccc/commit/52156f9df9cae9e0b71b77b49cda0e4d73e76142), [`63606db`](https://github.com/ckb-devrel/ccc/commit/63606db908f95bfc857430083932144d1ef4deef), [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b), [`1043c2b`](https://github.com/ckb-devrel/ccc/commit/1043c2bc211ec283b88dba3b81feef98e4185c0e), [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11), [`1f999f8`](https://github.com/ckb-devrel/ccc/commit/1f999f854beb255b3cd9dbbc5a7268e75442b3db), [`a69a9dc`](https://github.com/ckb-devrel/ccc/commit/a69a9dc0c722f7b4cfa36b2ae8ecba4dcde0db90), [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895)]:
  - @ckb-ccc/core@0.0.13

## 0.0.13-alpha.8

### Patch Changes

- Updated dependencies [[`8629449`](https://github.com/ckb-devrel/ccc/commit/86294490e76fc2a1cee20f827883e02fceca6e8b), [`52156f9`](https://github.com/ckb-devrel/ccc/commit/52156f9df9cae9e0b71b77b49cda0e4d73e76142), [`63606db`](https://github.com/ckb-devrel/ccc/commit/63606db908f95bfc857430083932144d1ef4deef)]:
  - @ckb-ccc/core@0.0.13-alpha.8

## 0.0.13-alpha.7

### Patch Changes

- [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): more APIs. Since parsing.

- [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findCells

- Updated dependencies [[`1043c2b`](https://github.com/ckb-devrel/ccc/commit/1043c2bc211ec283b88dba3b81feef98e4185c0e), [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11), [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895)]:
  - @ckb-ccc/core@0.0.13-alpha.7

## 0.0.13-alpha.6

### Patch Changes

- [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): completeInputs

- Updated dependencies [[`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97), [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b)]:
  - @ckb-ccc/core@0.0.13-alpha.6

## 0.0.13-alpha.5

### Patch Changes

- [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): recordCells should not add usableCells

- Updated dependencies [[`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97)]:
  - @ckb-ccc/core@0.0.13-alpha.5

## 0.0.13-alpha.4

### Patch Changes

- [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): node.js websocket

- Updated dependencies [[`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f), [`642f731`](https://github.com/ckb-devrel/ccc/commit/642f7317f4951ef801f1245aea96c40b4b6fb73e)]:
  - @ckb-ccc/core@0.0.13-alpha.4

## 0.0.13-alpha.3

### Patch Changes

- Updated dependencies [[`1f999f8`](https://github.com/ckb-devrel/ccc/commit/1f999f854beb255b3cd9dbbc5a7268e75442b3db)]:
  - @ckb-ccc/core@0.0.13-alpha.3

## 0.0.13-alpha.2

### Patch Changes

- Updated dependencies [[`96dbb61`](https://github.com/ckb-devrel/ccc/commit/96dbb6107d2071b9383350ddd578557746227054)]:
  - @ckb-ccc/core@0.0.13-alpha.2

## 0.0.13-alpha.1

### Patch Changes

- Updated dependencies [[`3378e85`](https://github.com/ckb-devrel/ccc/commit/3378e85b32797f5cdc1943b9ecaca1fd1d9fad5e), [`a69a9dc`](https://github.com/ckb-devrel/ccc/commit/a69a9dc0c722f7b4cfa36b2ae8ecba4dcde0db90)]:
  - @ckb-ccc/core@0.0.13-alpha.1

## 0.0.13-alpha.0

### Patch Changes

- Updated dependencies [[`600cc13`](https://github.com/ckb-devrel/ccc/commit/600cc137ac6eb7c5b2533670de6df29d82f1b9e1)]:
  - @ckb-ccc/core@0.0.13-alpha.0

## 0.0.12

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/core@0.0.12
