# @ckb-ccc/ccc

## 1.1.10
### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/eip6963@1.0.18
  - @ckb-ccc/joy-id@1.0.18
  - @ckb-ccc/nip07@1.0.18
  - @ckb-ccc/okx@1.0.18
  - @ckb-ccc/rei@1.0.18
  - @ckb-ccc/shell@1.1.10
  - @ckb-ccc/uni-sat@1.0.18
  - @ckb-ccc/utxo-global@1.0.18
  - @ckb-ccc/xverse@1.0.18

## 1.1.9

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/eip6963@1.0.17
  - @ckb-ccc/joy-id@1.0.17
  - @ckb-ccc/nip07@1.0.17
  - @ckb-ccc/okx@1.0.17
  - @ckb-ccc/rei@1.0.17
  - @ckb-ccc/shell@1.1.9
  - @ckb-ccc/uni-sat@1.0.17
  - @ckb-ccc/utxo-global@1.0.17
  - @ckb-ccc/xverse@1.0.17

## 1.1.8

### Patch Changes

- Updated dependencies [[`90b6e9f`](https://github.com/ckb-devrel/ccc/commit/90b6e9fee543b6ee16b96e27d6f86ff33fc57029), [`7bed665`](https://github.com/ckb-devrel/ccc/commit/7bed665f6c9444b37893f1c6c8c3cbca344a6ca4), [`29a2e22`](https://github.com/ckb-devrel/ccc/commit/29a2e223b902ed23523e4948ab3fca793f9e5b01)]:
  - @ckb-ccc/joy-id@1.0.16
  - @ckb-ccc/rei@1.0.16
  - @ckb-ccc/shell@1.1.8
  - @ckb-ccc/eip6963@1.0.16
  - @ckb-ccc/nip07@1.0.16
  - @ckb-ccc/okx@1.0.16
  - @ckb-ccc/uni-sat@1.0.16
  - @ckb-ccc/utxo-global@1.0.16
  - @ckb-ccc/xverse@1.0.16

## 1.1.3

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/eip6963@1.0.11
  - @ckb-ccc/joy-id@1.0.11
  - @ckb-ccc/nip07@1.0.11
  - @ckb-ccc/okx@1.0.11
  - @ckb-ccc/rei@1.0.11
  - @ckb-ccc/shell@1.1.3
  - @ckb-ccc/uni-sat@1.0.11
  - @ckb-ccc/utxo-global@1.0.11
  - @ckb-ccc/xverse@1.0.11

## 1.1.2

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/shell@1.1.2
  - @ckb-ccc/eip6963@1.0.10
  - @ckb-ccc/joy-id@1.0.10
  - @ckb-ccc/nip07@1.0.10
  - @ckb-ccc/okx@1.0.10
  - @ckb-ccc/rei@1.0.10
  - @ckb-ccc/uni-sat@1.0.10
  - @ckb-ccc/utxo-global@1.0.10
  - @ckb-ccc/xverse@1.0.10

## 1.1.1

### Patch Changes

- Updated dependencies []:
  - @ckb-ccc/shell@1.1.1
  - @ckb-ccc/eip6963@1.0.9
  - @ckb-ccc/joy-id@1.0.9
  - @ckb-ccc/nip07@1.0.9
  - @ckb-ccc/okx@1.0.9
  - @ckb-ccc/rei@1.0.9
  - @ckb-ccc/uni-sat@1.0.9
  - @ckb-ccc/utxo-global@1.0.9
  - @ckb-ccc/xverse@1.0.9

## 1.1.0

### Minor Changes

- [#130](https://github.com/ckb-devrel/ccc/pull/130) [`8c97c85`](https://github.com/ckb-devrel/ccc/commit/8c97c851db4a2d940c7e59116ca7620cfd0afae1) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: export backend packages in @ckb-ccc/shell

### Patch Changes

- Updated dependencies [[`8c97c85`](https://github.com/ckb-devrel/ccc/commit/8c97c851db4a2d940c7e59116ca7620cfd0afae1)]:
  - @ckb-ccc/shell@1.1.0
  - @ckb-ccc/eip6963@1.0.8
  - @ckb-ccc/joy-id@1.0.8
  - @ckb-ccc/nip07@1.0.8
  - @ckb-ccc/okx@1.0.8
  - @ckb-ccc/rei@1.0.8
  - @ckb-ccc/uni-sat@1.0.8
  - @ckb-ccc/utxo-global@1.0.8
  - @ckb-ccc/xverse@1.0.8

## 1.0.7

### Patch Changes

- Updated dependencies [[`01263bd`](https://github.com/ckb-devrel/ccc/commit/01263bd8c601fa8fcdfa24be52601716e1864843)]:
  - @ckb-ccc/core@1.2.3
  - @ckb-ccc/eip6963@1.0.7
  - @ckb-ccc/joy-id@1.0.7
  - @ckb-ccc/nip07@1.0.7
  - @ckb-ccc/okx@1.0.7
  - @ckb-ccc/rei@1.0.7
  - @ckb-ccc/uni-sat@1.0.7
  - @ckb-ccc/utxo-global@1.0.7
  - @ckb-ccc/xverse@1.0.7

## 1.0.6

### Patch Changes

- Updated dependencies [[`7886e3d`](https://github.com/ckb-devrel/ccc/commit/7886e3d89e9ca8f3514a2044c6dd4e8ec6b49933)]:
  - @ckb-ccc/core@1.2.2
  - @ckb-ccc/eip6963@1.0.6
  - @ckb-ccc/joy-id@1.0.6
  - @ckb-ccc/nip07@1.0.6
  - @ckb-ccc/okx@1.0.6
  - @ckb-ccc/rei@1.0.6
  - @ckb-ccc/uni-sat@1.0.6
  - @ckb-ccc/utxo-global@1.0.6
  - @ckb-ccc/xverse@1.0.6

## 1.0.5

### Patch Changes

- Updated dependencies [[`94e2618`](https://github.com/ckb-devrel/ccc/commit/94e26182515e09d6086ec5b653d091f117a499e6)]:
  - @ckb-ccc/core@1.2.1
  - @ckb-ccc/eip6963@1.0.5
  - @ckb-ccc/joy-id@1.0.5
  - @ckb-ccc/nip07@1.0.5
  - @ckb-ccc/okx@1.0.5
  - @ckb-ccc/rei@1.0.5
  - @ckb-ccc/uni-sat@1.0.5
  - @ckb-ccc/utxo-global@1.0.5
  - @ckb-ccc/xverse@1.0.5

## 1.0.4

### Patch Changes

- Updated dependencies [[`128e87b`](https://github.com/ckb-devrel/ccc/commit/128e87b5ca3e97bfe7842e76f786aa6aec010797)]:
  - @ckb-ccc/core@1.2.0
  - @ckb-ccc/eip6963@1.0.4
  - @ckb-ccc/joy-id@1.0.4
  - @ckb-ccc/nip07@1.0.4
  - @ckb-ccc/okx@1.0.4
  - @ckb-ccc/rei@1.0.4
  - @ckb-ccc/uni-sat@1.0.4
  - @ckb-ccc/utxo-global@1.0.4
  - @ckb-ccc/xverse@1.0.4

## 1.0.3

### Patch Changes

- Updated dependencies [[`925991c`](https://github.com/ckb-devrel/ccc/commit/925991c8a24b1f34667e30b28b69812e936e3928)]:
  - @ckb-ccc/core@1.1.1
  - @ckb-ccc/eip6963@1.0.3
  - @ckb-ccc/joy-id@1.0.3
  - @ckb-ccc/nip07@1.0.3
  - @ckb-ccc/okx@1.0.3
  - @ckb-ccc/rei@1.0.3
  - @ckb-ccc/uni-sat@1.0.3
  - @ckb-ccc/utxo-global@1.0.3
  - @ckb-ccc/xverse@1.0.3

## 1.0.2

### Patch Changes

- Updated dependencies [[`ddc0a28`](https://github.com/ckb-devrel/ccc/commit/ddc0a281c3d1dfa6ebc990dae92994f026dfddcc)]:
  - @ckb-ccc/core@1.1.0
  - @ckb-ccc/eip6963@1.0.2
  - @ckb-ccc/joy-id@1.0.2
  - @ckb-ccc/nip07@1.0.2
  - @ckb-ccc/okx@1.0.2
  - @ckb-ccc/rei@1.0.2
  - @ckb-ccc/uni-sat@1.0.2
  - @ckb-ccc/utxo-global@1.0.2
  - @ckb-ccc/xverse@1.0.2

## 1.0.1

### Patch Changes

- Updated dependencies [[`719055b`](https://github.com/ckb-devrel/ccc/commit/719055b404f31b40362f51714b9f11c85b857581), [`94caaca`](https://github.com/ckb-devrel/ccc/commit/94caaca11c63752a25282d42f51161c94397dec6)]:
  - @ckb-ccc/core@1.0.1
  - @ckb-ccc/eip6963@1.0.1
  - @ckb-ccc/joy-id@1.0.1
  - @ckb-ccc/nip07@1.0.1
  - @ckb-ccc/okx@1.0.1
  - @ckb-ccc/rei@1.0.1
  - @ckb-ccc/uni-sat@1.0.1
  - @ckb-ccc/utxo-global@1.0.1
  - @ckb-ccc/xverse@1.0.1

## 1.0.0

### Major Changes

- [#107](https://github.com/ckb-devrel/ccc/pull/107) [`b99f55f`](https://github.com/ckb-devrel/ccc/commit/b99f55f74e64106391ce53f7d0bd0fa7522023cc) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: molecule

### Patch Changes

- Updated dependencies [[`b99f55f`](https://github.com/ckb-devrel/ccc/commit/b99f55f74e64106391ce53f7d0bd0fa7522023cc)]:
  - @ckb-ccc/core@1.0.0
  - @ckb-ccc/eip6963@1.0.0
  - @ckb-ccc/joy-id@1.0.0
  - @ckb-ccc/nip07@1.0.0
  - @ckb-ccc/okx@1.0.0
  - @ckb-ccc/rei@1.0.0
  - @ckb-ccc/uni-sat@1.0.0
  - @ckb-ccc/utxo-global@1.0.0
  - @ckb-ccc/xverse@1.0.0

## 0.0.18

### Patch Changes

- Updated dependencies [[`c1cb910`](https://github.com/ckb-devrel/ccc/commit/c1cb91091780c7b33fbbd683ef8edc9f11452ecd)]:
  - @ckb-ccc/core@0.1.2
  - @ckb-ccc/eip6963@0.0.18
  - @ckb-ccc/joy-id@0.0.18
  - @ckb-ccc/nip07@0.0.18
  - @ckb-ccc/okx@0.0.18
  - @ckb-ccc/rei@0.0.18
  - @ckb-ccc/uni-sat@0.0.18
  - @ckb-ccc/utxo-global@0.0.18
  - @ckb-ccc/xverse@0.0.17

## 0.0.17

### Patch Changes

- [#101](https://github.com/ckb-devrel/ccc/pull/101) [`d9affcc`](https://github.com/ckb-devrel/ccc/commit/d9affcc01c7b839b227e4d79bcb66e717577502a) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix: commonjs

- Updated dependencies [[`d9affcc`](https://github.com/ckb-devrel/ccc/commit/d9affcc01c7b839b227e4d79bcb66e717577502a)]:
  - @ckb-ccc/utxo-global@0.0.17
  - @ckb-ccc/eip6963@0.0.17
  - @ckb-ccc/uni-sat@0.0.17
  - @ckb-ccc/joy-id@0.0.17
  - @ckb-ccc/xverse@0.0.16
  - @ckb-ccc/nip07@0.0.17
  - @ckb-ccc/core@0.1.1
  - @ckb-ccc/okx@0.0.17
  - @ckb-ccc/rei@0.0.17

## 0.0.16

### Patch Changes

- [#69](https://github.com/ckb-devrel/ccc/pull/69) [`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Client.waitTransaction

- [#70](https://github.com/ckb-devrel/ccc/pull/70) [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): websocket transport

- [#96](https://github.com/ckb-devrel/ccc/pull/96) [`e63a06e`](https://github.com/ckb-devrel/ccc/commit/e63a06ee75ac8595208d216dec88a4228c465e23) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: support doge signer

- [#67](https://github.com/ckb-devrel/ccc/pull/67) [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): auto fee rate

- [#60](https://github.com/ckb-devrel/ccc/pull/60) [`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: support Xverse

- Updated dependencies [[`a3d5359`](https://github.com/ckb-devrel/ccc/commit/a3d53595f6dd11f2f59cdf0086b3d7ce558a2fdd), [`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a), [`f07a506`](https://github.com/ckb-devrel/ccc/commit/f07a506bd6fc27fe659a17d2f7baaeec54716d81), [`f21d7e4`](https://github.com/ckb-devrel/ccc/commit/f21d7e4cf422edab4a836ef6d678b620594fef8d), [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366), [`1720d5a`](https://github.com/ckb-devrel/ccc/commit/1720d5a398543f1c6e24763eeaf15d84cd2214bf), [`e63a06e`](https://github.com/ckb-devrel/ccc/commit/e63a06ee75ac8595208d216dec88a4228c465e23), [`91832b1`](https://github.com/ckb-devrel/ccc/commit/91832b103c5d7a1272060639f77754a1c731a753), [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f), [`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34), [`50f2ce0`](https://github.com/ckb-devrel/ccc/commit/50f2ce08e74cb3fbeae926267d42e28b426fd7f4), [`4709384`](https://github.com/ckb-devrel/ccc/commit/4709384e37188991cb937b16f99f47ca82c912b8)]:
  - @ckb-ccc/core@0.1.0
  - @ckb-ccc/eip6963@0.0.16
  - @ckb-ccc/joy-id@0.0.16
  - @ckb-ccc/nip07@0.0.16
  - @ckb-ccc/okx@0.0.16
  - @ckb-ccc/rei@0.0.16
  - @ckb-ccc/uni-sat@0.0.16
  - @ckb-ccc/utxo-global@0.0.16
  - @ckb-ccc/xverse@0.0.15

## 0.0.16-alpha.9

### Patch Changes

- Updated dependencies [[`f07a506`](https://github.com/ckb-devrel/ccc/commit/f07a506bd6fc27fe659a17d2f7baaeec54716d81)]:
  - @ckb-ccc/core@0.1.0-alpha.7
  - @ckb-ccc/eip6963@0.0.16-alpha.7
  - @ckb-ccc/joy-id@0.0.16-alpha.7
  - @ckb-ccc/nip07@0.0.16-alpha.7
  - @ckb-ccc/okx@0.0.16-alpha.7
  - @ckb-ccc/rei@0.0.16-alpha.7
  - @ckb-ccc/uni-sat@0.0.16-alpha.7
  - @ckb-ccc/utxo-global@0.0.16-alpha.7
  - @ckb-ccc/xverse@0.0.15-alpha.9

## 0.0.16-alpha.8

### Patch Changes

- Updated dependencies [[`4709384`](https://github.com/ckb-devrel/ccc/commit/4709384e37188991cb937b16f99f47ca82c912b8)]:
  - @ckb-ccc/core@0.1.0-alpha.6
  - @ckb-ccc/eip6963@0.0.16-alpha.6
  - @ckb-ccc/joy-id@0.0.16-alpha.6
  - @ckb-ccc/nip07@0.0.16-alpha.6
  - @ckb-ccc/okx@0.0.16-alpha.6
  - @ckb-ccc/rei@0.0.16-alpha.6
  - @ckb-ccc/uni-sat@0.0.16-alpha.6
  - @ckb-ccc/utxo-global@0.0.16-alpha.6
  - @ckb-ccc/xverse@0.0.15-alpha.8

## 0.0.16-alpha.7

### Patch Changes

- Updated dependencies [[`91832b1`](https://github.com/ckb-devrel/ccc/commit/91832b103c5d7a1272060639f77754a1c731a753)]:
  - @ckb-ccc/xverse@0.0.15-alpha.7

## 0.0.16-alpha.6

### Patch Changes

- Updated dependencies [[`50f2ce0`](https://github.com/ckb-devrel/ccc/commit/50f2ce08e74cb3fbeae926267d42e28b426fd7f4)]:
  - @ckb-ccc/core@0.1.0-alpha.5
  - @ckb-ccc/eip6963@0.0.16-alpha.5
  - @ckb-ccc/joy-id@0.0.16-alpha.5
  - @ckb-ccc/nip07@0.0.16-alpha.5
  - @ckb-ccc/okx@0.0.16-alpha.5
  - @ckb-ccc/rei@0.0.16-alpha.5
  - @ckb-ccc/uni-sat@0.0.16-alpha.5
  - @ckb-ccc/utxo-global@0.0.16-alpha.5
  - @ckb-ccc/xverse@0.0.15-alpha.6

## 0.0.16-alpha.5

### Patch Changes

- Updated dependencies [[`f21d7e4`](https://github.com/ckb-devrel/ccc/commit/f21d7e4cf422edab4a836ef6d678b620594fef8d)]:
  - @ckb-ccc/core@0.1.0-alpha.4
  - @ckb-ccc/eip6963@0.0.16-alpha.4
  - @ckb-ccc/joy-id@0.0.16-alpha.4
  - @ckb-ccc/nip07@0.0.16-alpha.4
  - @ckb-ccc/okx@0.0.16-alpha.4
  - @ckb-ccc/rei@0.0.16-alpha.4
  - @ckb-ccc/uni-sat@0.0.16-alpha.4
  - @ckb-ccc/utxo-global@0.0.16-alpha.4
  - @ckb-ccc/xverse@0.0.15-alpha.5

## 0.0.16-alpha.4

### Patch Changes

- Updated dependencies [[`a3d5359`](https://github.com/ckb-devrel/ccc/commit/a3d53595f6dd11f2f59cdf0086b3d7ce558a2fdd)]:
  - @ckb-ccc/core@0.0.16-alpha.3
  - @ckb-ccc/eip6963@0.0.16-alpha.3
  - @ckb-ccc/joy-id@0.0.16-alpha.3
  - @ckb-ccc/nip07@0.0.16-alpha.3
  - @ckb-ccc/okx@0.0.16-alpha.3
  - @ckb-ccc/rei@0.0.16-alpha.3
  - @ckb-ccc/uni-sat@0.0.16-alpha.3
  - @ckb-ccc/utxo-global@0.0.16-alpha.3
  - @ckb-ccc/xverse@0.0.15-alpha.4

## 0.0.16-alpha.3

### Patch Changes

- [#70](https://github.com/ckb-devrel/ccc/pull/70) [`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): websocket transport

- Updated dependencies [[`acfc050`](https://github.com/ckb-devrel/ccc/commit/acfc0502cd6beb48b9310dec8411dcd630507366)]:
  - @ckb-ccc/core@0.0.16-alpha.2
  - @ckb-ccc/eip6963@0.0.16-alpha.2
  - @ckb-ccc/joy-id@0.0.16-alpha.2
  - @ckb-ccc/nip07@0.0.16-alpha.2
  - @ckb-ccc/okx@0.0.16-alpha.2
  - @ckb-ccc/rei@0.0.16-alpha.2
  - @ckb-ccc/uni-sat@0.0.16-alpha.2
  - @ckb-ccc/utxo-global@0.0.16-alpha.2
  - @ckb-ccc/xverse@0.0.15-alpha.3

## 0.0.16-alpha.2

### Patch Changes

- [#69](https://github.com/ckb-devrel/ccc/pull/69) [`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Client.waitTransaction

- [#67](https://github.com/ckb-devrel/ccc/pull/67) [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): auto fee rate

- Updated dependencies [[`8824ff2`](https://github.com/ckb-devrel/ccc/commit/8824ff27af3b76186f1a7d6db8c907cd66f09d6a), [`c092988`](https://github.com/ckb-devrel/ccc/commit/c092988e7765b9ac79498d6bd72a6a2f62859b6f)]:
  - @ckb-ccc/core@0.0.16-alpha.1
  - @ckb-ccc/eip6963@0.0.16-alpha.1
  - @ckb-ccc/joy-id@0.0.16-alpha.1
  - @ckb-ccc/nip07@0.0.16-alpha.1
  - @ckb-ccc/okx@0.0.16-alpha.1
  - @ckb-ccc/rei@0.0.16-alpha.1
  - @ckb-ccc/uni-sat@0.0.16-alpha.1
  - @ckb-ccc/utxo-global@0.0.16-alpha.1
  - @ckb-ccc/xverse@0.0.15-alpha.2

## 0.0.16-alpha.1

### Patch Changes

- Updated dependencies [[`1720d5a`](https://github.com/ckb-devrel/ccc/commit/1720d5a398543f1c6e24763eeaf15d84cd2214bf)]:
  - @ckb-ccc/core@0.0.16-alpha.0
  - @ckb-ccc/eip6963@0.0.16-alpha.0
  - @ckb-ccc/joy-id@0.0.16-alpha.0
  - @ckb-ccc/nip07@0.0.16-alpha.0
  - @ckb-ccc/okx@0.0.16-alpha.0
  - @ckb-ccc/rei@0.0.16-alpha.0
  - @ckb-ccc/uni-sat@0.0.16-alpha.0
  - @ckb-ccc/utxo-global@0.0.16-alpha.0
  - @ckb-ccc/xverse@0.0.15-alpha.1

## 0.0.16-alpha.0

### Patch Changes

- [#60](https://github.com/ckb-devrel/ccc/pull/60) [`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: support Xverse

- Updated dependencies [[`e904963`](https://github.com/ckb-devrel/ccc/commit/e904963a16f12c410d861eb3ae01b87d68cb3e34)]:
  - @ckb-ccc/xverse@0.0.15-alpha.0

## 0.0.15

### Patch Changes

- Updated dependencies [[`8f2560a`](https://github.com/ckb-devrel/ccc/commit/8f2560ab0e5619654fff7c5eacda8425385f908e)]:
  - @ckb-ccc/core@0.0.15
  - @ckb-ccc/eip6963@0.0.15
  - @ckb-ccc/joy-id@0.0.15
  - @ckb-ccc/nip07@0.0.15
  - @ckb-ccc/okx@0.0.15
  - @ckb-ccc/rei@0.0.15
  - @ckb-ccc/uni-sat@0.0.15
  - @ckb-ccc/utxo-global@0.0.15

## 0.0.14

### Patch Changes

- [#56](https://github.com/ckb-devrel/ccc/pull/56) [`f13f4d3`](https://github.com/ckb-devrel/ccc/commit/f13f4d319ca66b571029a65e945e3a038bfeea25) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findTransactions

- [#48](https://github.com/ckb-devrel/ccc/pull/48) [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(okx): make okx wallet happy

- Updated dependencies [[`5e942f8`](https://github.com/ckb-devrel/ccc/commit/5e942f8f1ed678abdb7ab9716f5449f0714cea53), [`f5b5938`](https://github.com/ckb-devrel/ccc/commit/f5b5938ab8f9c0a338dfd6765fe717f7ad1b1dd8), [`f13f4d3`](https://github.com/ckb-devrel/ccc/commit/f13f4d319ca66b571029a65e945e3a038bfeea25), [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6), [`7ba62a0`](https://github.com/ckb-devrel/ccc/commit/7ba62a056f17808fe5684786c00c2dff80bb7d1d), [`2164efd`](https://github.com/ckb-devrel/ccc/commit/2164efd6d834c1917ad5f4a514dc25941f937185), [`e5bd2ad`](https://github.com/ckb-devrel/ccc/commit/e5bd2ad5de4b42a22c422ecfc42056750f69b88b), [`aae3e06`](https://github.com/ckb-devrel/ccc/commit/aae3e0679fb940dd8c12ac9be12a4b53277a339d)]:
  - @ckb-ccc/core@0.0.14
  - @ckb-ccc/okx@0.0.14
  - @ckb-ccc/eip6963@0.0.14
  - @ckb-ccc/joy-id@0.0.14
  - @ckb-ccc/nip07@0.0.14
  - @ckb-ccc/rei@0.0.14
  - @ckb-ccc/uni-sat@0.0.14
  - @ckb-ccc/utxo-global@0.0.14

## 0.0.14-alpha.2

### Patch Changes

- Updated dependencies [[`7ba62a0`](https://github.com/ckb-devrel/ccc/commit/7ba62a056f17808fe5684786c00c2dff80bb7d1d)]:
  - @ckb-ccc/core@0.0.14-alpha.2
  - @ckb-ccc/eip6963@0.0.14-alpha.2
  - @ckb-ccc/joy-id@0.0.14-alpha.2
  - @ckb-ccc/nip07@0.0.14-alpha.2
  - @ckb-ccc/okx@0.0.14-alpha.2
  - @ckb-ccc/rei@0.0.14-alpha.2
  - @ckb-ccc/uni-sat@0.0.14-alpha.2
  - @ckb-ccc/utxo-global@0.0.14-alpha.2

## 0.0.14-alpha.1

### Patch Changes

- [#48](https://github.com/ckb-devrel/ccc/pull/48) [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(okx): make okx wallet happy

- Updated dependencies [[`5e942f8`](https://github.com/ckb-devrel/ccc/commit/5e942f8f1ed678abdb7ab9716f5449f0714cea53), [`4fb114b`](https://github.com/ckb-devrel/ccc/commit/4fb114bc421c7250eed7388c16f1c026875153e6), [`e5bd2ad`](https://github.com/ckb-devrel/ccc/commit/e5bd2ad5de4b42a22c422ecfc42056750f69b88b), [`aae3e06`](https://github.com/ckb-devrel/ccc/commit/aae3e0679fb940dd8c12ac9be12a4b53277a339d)]:
  - @ckb-ccc/core@0.0.14-alpha.1
  - @ckb-ccc/okx@0.0.14-alpha.1
  - @ckb-ccc/eip6963@0.0.14-alpha.1
  - @ckb-ccc/joy-id@0.0.14-alpha.1
  - @ckb-ccc/nip07@0.0.14-alpha.1
  - @ckb-ccc/rei@0.0.14-alpha.1
  - @ckb-ccc/uni-sat@0.0.14-alpha.1
  - @ckb-ccc/utxo-global@0.0.14-alpha.1

## 0.0.14-alpha.0

### Patch Changes

- Updated dependencies [[`f5b5938`](https://github.com/ckb-devrel/ccc/commit/f5b5938ab8f9c0a338dfd6765fe717f7ad1b1dd8), [`2164efd`](https://github.com/ckb-devrel/ccc/commit/2164efd6d834c1917ad5f4a514dc25941f937185)]:
  - @ckb-ccc/core@0.0.14-alpha.0
  - @ckb-ccc/eip6963@0.0.14-alpha.0
  - @ckb-ccc/joy-id@0.0.14-alpha.0
  - @ckb-ccc/nip07@0.0.14-alpha.0
  - @ckb-ccc/okx@0.0.14-alpha.0
  - @ckb-ccc/rei@0.0.14-alpha.0
  - @ckb-ccc/uni-sat@0.0.14-alpha.0
  - @ckb-ccc/utxo-global@0.0.14-alpha.0

## 0.0.13

### Patch Changes

- [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): recordCells should not add usableCells

- [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): node.js websocket

- [#25](https://github.com/ckb-devrel/ccc/pull/25) [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(utxo-global): switchNetwork

- [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): completeInputs

- [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): more APIs. Since parsing.

- [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findCells

- Updated dependencies [[`3378e85`](https://github.com/ckb-devrel/ccc/commit/3378e85b32797f5cdc1943b9ecaca1fd1d9fad5e), [`8629449`](https://github.com/ckb-devrel/ccc/commit/86294490e76fc2a1cee20f827883e02fceca6e8b), [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97), [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f), [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97), [`600cc13`](https://github.com/ckb-devrel/ccc/commit/600cc137ac6eb7c5b2533670de6df29d82f1b9e1), [`642f731`](https://github.com/ckb-devrel/ccc/commit/642f7317f4951ef801f1245aea96c40b4b6fb73e), [`96dbb61`](https://github.com/ckb-devrel/ccc/commit/96dbb6107d2071b9383350ddd578557746227054), [`0462a4e`](https://github.com/ckb-devrel/ccc/commit/0462a4ee101926f0da857173626dc4ab879e3b56), [`588db84`](https://github.com/ckb-devrel/ccc/commit/588db8434d76a5a9ea6dd1d4c5b03f356ec1fb7d), [`52156f9`](https://github.com/ckb-devrel/ccc/commit/52156f9df9cae9e0b71b77b49cda0e4d73e76142), [`63606db`](https://github.com/ckb-devrel/ccc/commit/63606db908f95bfc857430083932144d1ef4deef), [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b), [`1043c2b`](https://github.com/ckb-devrel/ccc/commit/1043c2bc211ec283b88dba3b81feef98e4185c0e), [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11), [`1f999f8`](https://github.com/ckb-devrel/ccc/commit/1f999f854beb255b3cd9dbbc5a7268e75442b3db), [`a69a9dc`](https://github.com/ckb-devrel/ccc/commit/a69a9dc0c722f7b4cfa36b2ae8ecba4dcde0db90), [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895)]:
  - @ckb-ccc/core@0.0.13
  - @ckb-ccc/eip6963@0.0.13
  - @ckb-ccc/joy-id@0.0.13
  - @ckb-ccc/nip07@0.0.13
  - @ckb-ccc/okx@0.0.13
  - @ckb-ccc/rei@0.0.13
  - @ckb-ccc/uni-sat@0.0.13
  - @ckb-ccc/utxo-global@0.0.13

## 0.0.13-alpha.8

### Patch Changes

- Updated dependencies [[`8629449`](https://github.com/ckb-devrel/ccc/commit/86294490e76fc2a1cee20f827883e02fceca6e8b), [`52156f9`](https://github.com/ckb-devrel/ccc/commit/52156f9df9cae9e0b71b77b49cda0e4d73e76142), [`63606db`](https://github.com/ckb-devrel/ccc/commit/63606db908f95bfc857430083932144d1ef4deef)]:
  - @ckb-ccc/core@0.0.13-alpha.8
  - @ckb-ccc/eip6963@0.0.13-alpha.8
  - @ckb-ccc/joy-id@0.0.13-alpha.8
  - @ckb-ccc/nip07@0.0.13-alpha.8
  - @ckb-ccc/okx@0.0.13-alpha.8
  - @ckb-ccc/rei@0.0.13-alpha.8
  - @ckb-ccc/uni-sat@0.0.13-alpha.8
  - @ckb-ccc/utxo-global@0.0.13-alpha.8

## 0.0.13-alpha.7

### Patch Changes

- [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): more APIs. Since parsing.

- [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): Signer.findCells

- Updated dependencies [[`1043c2b`](https://github.com/ckb-devrel/ccc/commit/1043c2bc211ec283b88dba3b81feef98e4185c0e), [`079e20e`](https://github.com/ckb-devrel/ccc/commit/079e20ef14cf9a7c06bbaddf3e92cbfbb005da11), [`ed154d1`](https://github.com/ckb-devrel/ccc/commit/ed154d189e239907ad686ec51ac8133b6d5eb895)]:
  - @ckb-ccc/core@0.0.13-alpha.7
  - @ckb-ccc/eip6963@0.0.13-alpha.7
  - @ckb-ccc/joy-id@0.0.13-alpha.7
  - @ckb-ccc/nip07@0.0.13-alpha.7
  - @ckb-ccc/okx@0.0.13-alpha.7
  - @ckb-ccc/rei@0.0.13-alpha.7
  - @ckb-ccc/uni-sat@0.0.13-alpha.7
  - @ckb-ccc/utxo-global@0.0.13-alpha.7

## 0.0.13-alpha.6

### Patch Changes

- [#25](https://github.com/ckb-devrel/ccc/pull/25) [`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(utxo-global): switchNetwork

- [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): completeInputs

- Updated dependencies [[`69c10fd`](https://github.com/ckb-devrel/ccc/commit/69c10fdfcd507433c13b15d17015dca4687afb97), [`44c7fee`](https://github.com/ckb-devrel/ccc/commit/44c7feed37369836268fba21884418682f15254b)]:
  - @ckb-ccc/utxo-global@0.0.13-alpha.6
  - @ckb-ccc/core@0.0.13-alpha.6
  - @ckb-ccc/eip6963@0.0.13-alpha.6
  - @ckb-ccc/joy-id@0.0.13-alpha.6
  - @ckb-ccc/nip07@0.0.13-alpha.6
  - @ckb-ccc/okx@0.0.13-alpha.6
  - @ckb-ccc/rei@0.0.13-alpha.6
  - @ckb-ccc/uni-sat@0.0.13-alpha.6

## 0.0.13-alpha.5

### Patch Changes

- [`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97) Thanks [@Hanssen0](https://github.com/Hanssen0)! - fix(core): recordCells should not add usableCells

- Updated dependencies [[`6d62032`](https://github.com/ckb-devrel/ccc/commit/6d620326f42f8c48eff9deb95578cf28d7bf5c97)]:
  - @ckb-ccc/core@0.0.13-alpha.5
  - @ckb-ccc/eip6963@0.0.13-alpha.5
  - @ckb-ccc/joy-id@0.0.13-alpha.5
  - @ckb-ccc/nip07@0.0.13-alpha.5
  - @ckb-ccc/okx@0.0.13-alpha.5
  - @ckb-ccc/rei@0.0.13-alpha.5
  - @ckb-ccc/uni-sat@0.0.13-alpha.5
  - @ckb-ccc/utxo-global@0.0.13-alpha.5

## 0.0.13-alpha.4

### Patch Changes

- [`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat(core): node.js websocket

- Updated dependencies [[`3658797`](https://github.com/ckb-devrel/ccc/commit/3658797e67c42c56b20fa66481d0455ed019e69f), [`642f731`](https://github.com/ckb-devrel/ccc/commit/642f7317f4951ef801f1245aea96c40b4b6fb73e)]:
  - @ckb-ccc/core@0.0.13-alpha.4
  - @ckb-ccc/eip6963@0.0.13-alpha.4
  - @ckb-ccc/joy-id@0.0.13-alpha.4
  - @ckb-ccc/nip07@0.0.13-alpha.4
  - @ckb-ccc/okx@0.0.13-alpha.4
  - @ckb-ccc/rei@0.0.13-alpha.4
  - @ckb-ccc/uni-sat@0.0.13-alpha.4
  - @ckb-ccc/utxo-global@0.0.13-alpha.4

## 0.0.13-alpha.3

### Patch Changes

- Updated dependencies [[`1f999f8`](https://github.com/ckb-devrel/ccc/commit/1f999f854beb255b3cd9dbbc5a7268e75442b3db)]:
  - @ckb-ccc/core@0.0.13-alpha.3
  - @ckb-ccc/eip6963@0.0.13-alpha.3
  - @ckb-ccc/joy-id@0.0.13-alpha.3
  - @ckb-ccc/nip07@0.0.13-alpha.3
  - @ckb-ccc/okx@0.0.13-alpha.3
  - @ckb-ccc/rei@0.0.13-alpha.3
  - @ckb-ccc/uni-sat@0.0.13-alpha.3
  - @ckb-ccc/utxo-global@0.0.13-alpha.3

## 0.0.13-alpha.2

### Patch Changes

- Updated dependencies [[`96dbb61`](https://github.com/ckb-devrel/ccc/commit/96dbb6107d2071b9383350ddd578557746227054)]:
  - @ckb-ccc/core@0.0.13-alpha.2
  - @ckb-ccc/eip6963@0.0.13-alpha.2
  - @ckb-ccc/joy-id@0.0.13-alpha.2
  - @ckb-ccc/nip07@0.0.13-alpha.2
  - @ckb-ccc/okx@0.0.13-alpha.2
  - @ckb-ccc/rei@0.0.13-alpha.2
  - @ckb-ccc/uni-sat@0.0.13-alpha.2
  - @ckb-ccc/utxo-global@0.0.13-alpha.2

## 0.0.13-alpha.1

### Patch Changes

- Updated dependencies [[`3378e85`](https://github.com/ckb-devrel/ccc/commit/3378e85b32797f5cdc1943b9ecaca1fd1d9fad5e), [`588db84`](https://github.com/ckb-devrel/ccc/commit/588db8434d76a5a9ea6dd1d4c5b03f356ec1fb7d), [`a69a9dc`](https://github.com/ckb-devrel/ccc/commit/a69a9dc0c722f7b4cfa36b2ae8ecba4dcde0db90)]:
  - @ckb-ccc/core@0.0.13-alpha.1
  - @ckb-ccc/joy-id@0.0.13-alpha.1
  - @ckb-ccc/eip6963@0.0.13-alpha.1
  - @ckb-ccc/nip07@0.0.13-alpha.1
  - @ckb-ccc/okx@0.0.13-alpha.1
  - @ckb-ccc/rei@0.0.13-alpha.1
  - @ckb-ccc/uni-sat@0.0.13-alpha.1
  - @ckb-ccc/utxo-global@0.0.13-alpha.1

## 0.0.13-alpha.0

### Patch Changes

- Updated dependencies [[`600cc13`](https://github.com/ckb-devrel/ccc/commit/600cc137ac6eb7c5b2533670de6df29d82f1b9e1)]:
  - @ckb-ccc/core@0.0.13-alpha.0
  - @ckb-ccc/eip6963@0.0.13-alpha.0
  - @ckb-ccc/joy-id@0.0.13-alpha.0
  - @ckb-ccc/nip07@0.0.13-alpha.0
  - @ckb-ccc/okx@0.0.13-alpha.0
  - @ckb-ccc/rei@0.0.13-alpha.0
  - @ckb-ccc/uni-sat@0.0.13-alpha.0
  - @ckb-ccc/utxo-global@0.0.13-alpha.0

## 0.0.12

### Patch Changes

- [`591e779`](https://github.com/ckb-devrel/ccc/commit/591e7794ce3d07ceaad55b7a80d2277fe0aa9fe7) Thanks [@Hanssen0](https://github.com/Hanssen0)! - feat: custom SignersController

- Updated dependencies [[`6bee006`](https://github.com/ckb-devrel/ccc/commit/6bee006fbcb96986c65ca4d2d896fca21db2503b)]:
  - @ckb-ccc/nip07@0.0.12
  - @ckb-ccc/okx@0.0.12
  - @ckb-ccc/core@0.0.12
  - @ckb-ccc/eip6963@0.0.12
  - @ckb-ccc/joy-id@0.0.12
  - @ckb-ccc/rei@0.0.12
  - @ckb-ccc/uni-sat@0.0.12
  - @ckb-ccc/utxo-global@0.0.12

## 0.0.12-alpha.7

### Patch Changes

- Updated dependencies
  - @ckb-ccc/nip07@0.0.12-alpha.2
  - @ckb-ccc/okx@0.0.12-alpha.7
