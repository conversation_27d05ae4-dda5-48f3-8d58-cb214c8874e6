import { encodeSvgToImgSrc } from "./utils.js";

export const REI_SVG = encodeSvgToImgSrc(
  '<svg id="layer2" data-name="layer2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 88.24 70.95"><defs><style>.cls-1{fill:url(#linear_9)}.cls-2{fill:rgba(155,84,168,0)}</style><linearGradient id="linear_9" data-name="linear 9" x1="216.98" y1="-423.81" x2="170.95" y2="319.28" gradientTransform="translate(18.94 29.12) scale(.09 -.09)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#01f395"/><stop offset="1" stop-color="#01b56a"/></linearGradient></defs><g id="layer1" data-name="layer1"><g><rect class="cls-2" width="88.24" height="70.95"/><g><path class="cls-1" d="m19.08,0H4.9l49.8,70.95h14.19L19.08,0Z"/><path d="m69.08,44.08c9.93-2.84,16.92-9.85,16.92-21.5v-.2c0-6.18-2.12-11.44-5.87-15.29-4.56-4.45-11.54-7.08-20.46-7.08H24.09l7.97,11.37h26.59c9.22,0,14.7,4.12,14.7,11.72v.2c0,7.19-5.68,11.86-14.6,11.86h-9.98l25.11,35.79h14.37l-19.16-26.87Z"/><path d="m0,20.16l35.64,50.78h14.19L14.19,20.16H0Z"/></g></g></g></svg>',
);
