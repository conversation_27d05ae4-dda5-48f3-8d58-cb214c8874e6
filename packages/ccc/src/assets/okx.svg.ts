import { encodeSvgToImgSrc } from "./utils.js";

export const OKX_SVG = encodeSvgToImgSrc(
  '<svg width="1000" height="1000" viewBox="0 0 1000 1000" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="1000" height="1000" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M393.949 218.518H231.049C224.129 218.518 218.519 224.128 218.519 231.048V393.948C218.519 400.869 224.129 406.479 231.049 406.479H393.949C400.87 406.479 406.48 400.869 406.48 393.948V231.048C406.48 224.128 400.87 218.518 393.949 218.518ZM581.992 406.479H419.092C412.172 406.479 406.561 412.09 406.561 419.01V581.91C406.561 588.831 412.172 594.441 419.092 594.441H581.992C588.913 594.441 594.523 588.831 594.523 581.91V419.01C594.523 412.09 588.913 406.479 581.992 406.479ZM606.974 218.518H769.874C776.794 218.518 782.405 224.128 782.405 231.048V393.948C782.405 400.869 776.794 406.479 769.874 406.479H606.974C600.053 406.479 594.443 400.869 594.443 393.948V231.048C594.443 224.128 600.053 218.518 606.974 218.518ZM393.95 594.442H231.049C224.129 594.442 218.519 600.052 218.519 606.973V769.873C218.519 776.793 224.129 782.404 231.049 782.404H393.95C400.87 782.404 406.48 776.793 406.48 769.873V606.973C406.48 600.052 400.87 594.442 393.95 594.442ZM606.974 594.442H769.874C776.794 594.442 782.405 600.052 782.405 606.973V769.873C782.405 776.793 776.794 782.404 769.874 782.404H606.974C600.053 782.404 594.443 776.793 594.443 769.873V606.973C594.443 600.052 600.053 594.442 606.974 594.442Z" fill="white"/></svg>',
);
