import { encodeSvgToImgSrc } from "./utils.js";

export const UTXO_GLOBAL_SVG = encodeSvgToImgSrc(`
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="512" height="512" rx="256" fill="black"/>
    <g clip-path="url(#clip0_3306_46207)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M303.998 281.91C303.998 290.558 311.018 297.568 319.677 297.568H379.781C381.224 297.568 382.394 298.736 382.394 300.178V359.734V360.2H382.401C382.648 368.632 389.57 375.392 398.074 375.392C406.577 375.392 413.499 368.632 413.746 360.2H413.753V359.734V297.568V276.691C413.753 270.926 409.073 266.252 403.3 266.252H382.394H319.677C311.018 266.252 303.998 273.262 303.998 281.91Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M382.394 391.05C382.394 382.402 375.375 375.392 366.715 375.392H306.612C305.168 375.392 303.998 374.223 303.998 372.782V313.226V312.76H303.992C303.745 304.328 296.822 297.568 288.319 297.568C279.816 297.568 272.894 304.328 272.647 312.76H272.64V313.226V375.392V396.269C272.64 402.034 277.32 406.708 283.093 406.708H303.998H366.715C375.375 406.708 382.394 399.697 382.394 391.05Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M102.456 401.977C108.579 408.092 118.507 408.092 124.63 401.977L167.13 359.535C168.15 358.516 169.805 358.516 170.825 359.535L212.995 401.647L213.325 401.977L213.33 401.972C219.475 407.76 229.156 407.652 235.168 401.647C241.181 395.643 241.289 385.975 235.494 379.838L235.499 379.833L235.168 379.503L191.151 335.546L176.369 320.783C172.287 316.707 165.668 316.707 161.586 320.783L146.804 335.546L102.456 379.833C96.333 385.948 96.333 395.862 102.456 401.977Z" fill="#FF7201"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M102.291 269.894C108.414 263.779 118.342 263.779 124.465 269.894L166.965 312.336C167.985 313.355 169.64 313.355 170.66 312.336L212.83 270.224L213.16 269.894L213.165 269.899C219.31 264.111 228.991 264.219 235.003 270.224C241.016 276.228 241.125 285.896 235.329 292.033L235.334 292.038L235.003 292.367L190.986 336.325L176.204 351.088C172.122 355.164 165.503 355.164 161.421 351.088L146.639 336.325L102.291 292.038C96.1681 285.923 96.1681 276.009 102.291 269.894Z" fill="#FF7201"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M273.839 120.74C273.839 129.388 280.859 136.398 289.518 136.398H327.223C328.666 136.398 329.836 137.567 329.836 139.008L329.829 230.412V230.878H329.836C330.083 239.31 337.005 246.07 345.508 246.07C354.011 246.07 360.934 239.31 361.181 230.878H361.188V230.412L361.194 136.398V115.521C361.194 109.756 356.514 105.082 350.742 105.082H329.836H289.518C280.859 105.082 273.839 112.093 273.839 120.74Z" fill="#FF7201"/>
    <path d="M395.035 105.082C403.694 105.082 410.714 112.093 410.714 120.74C410.714 129.388 403.694 136.398 395.035 136.398C386.376 136.398 379.356 129.388 379.356 120.74C379.356 112.093 386.376 105.082 395.035 105.082Z" fill="#FF7201"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M239.114 120.646L239.114 120.658L239.114 120.67L239.112 199.095V199.562H239.106C238.859 207.994 231.936 214.754 223.433 214.754C214.93 214.754 208.008 207.994 207.761 199.562H207.754V199.101L207.754 199.096L207.754 199.091L207.756 120.658L207.756 120.184H207.763C208.014 111.756 214.934 105 223.435 105C231.935 105 238.856 111.756 239.107 120.184H239.114V120.646Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M207.755 230.412C207.755 221.764 200.735 214.754 192.075 214.754H131.972C130.528 214.754 129.358 213.586 129.358 212.144V121.272V120.806H129.352C129.105 112.374 122.182 105.614 113.679 105.614C105.176 105.614 98.2539 112.374 98.0069 120.806H98.0001V121.272V214.754V235.632C98.0001 241.397 102.68 246.07 108.453 246.07H129.358H192.075C200.735 246.07 207.755 239.06 207.755 230.412Z" fill="white"/>
    </g>
    <defs>
    <clipPath id="clip0_3306_46207">
    <rect width="316" height="302" fill="white" transform="translate(98 105)"/>
    </clipPath>
    </defs>
</svg>
`);
