{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true}, "projects": {"tap": {"type": "library", "root": "libs/tap", "entryFile": "index", "sourceRoot": "libs/tap/src", "compilerOptions": {"tsConfigPath": "libs/tap/tsconfig.lib.json"}}, "commons": {"type": "library", "root": "libs/commons", "entryFile": "index", "sourceRoot": "libs/commons/src", "compilerOptions": {"tsConfigPath": "libs/commons/tsconfig.lib.json"}}}}