{"name": "@ckb-ccc/examples", "version": "1.0.16", "description": "CCC code examples for CCC Playground", "author": "Hanssen0 <<EMAIL>>", "license": "MIT", "private": true, "homepage": "https://github.com/ckb-devrel/ccc", "repository": {"type": "git", "url": "git://github.com/ckb-devrel/ccc.git"}, "scripts": {"build": "rimraf ./dist && tsc", "lint": "eslint ./src", "format": "prettier --write . && eslint --fix ./src"}, "devDependencies": {"@eslint/js": "^9.1.1", "eslint": "^9.1.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "rimraf": "^5.0.5", "typescript": "^5.4.5", "typescript-eslint": "^7.7.0"}, "publishConfig": {"access": "public"}, "dependencies": {"@ckb-ccc/ccc": "workspace:*", "@ckb-ccc/playground": "file:./src/playground"}, "packageManager": "pnpm@10.8.1"}