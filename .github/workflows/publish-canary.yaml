name: Release to NPM Canary

permissions:
  contents: write

on: workflow_dispatch

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 22
      - uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: pnpm build:prepare
      - name: Build
        run: pnpm build

      - name: Version changesets
        run: pnpm changeset version --snapshot canary
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish to npm canary
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm changeset publish --tag canary
          createGithubReleases: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
