name: Build, lint and test

on: [pull_request, push]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 22
      - uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: pnpm install
      - name: Build
        run: pnpm build:all
      - name: Lint
        run: pnpm lint
      - name: Test
        run: pnpm test:ci
