{"private": true, "workspaces": ["packages/*"], "scripts": {"test": "vitest", "test:ci": "vitest run", "test:cov": "vitest run --coverage", "build:prepare": "pnpm -r --filter !./packages/demo --filter !./packages/faucet --filter !./packages/examples --filter !./packages/playground --filter !./packages/docs --filter !. install", "build": "pnpm -r --filter !./packages/demo --filter !./packages/faucet --filter !./packages/examples --filter !./packages/playground --filter !./packages/docs run build", "build:all": "pnpm -r run build", "lint": "pnpm -r run lint", "format": "pnpm -r run format", "sync:config": "pnpm -r --filter !./packages/demo --filter !./packages/faucet --filter !./packages/playground --filter !./packages/docs -c exec \"cp ../../config/* . && cp ../../config/.* .\"", "change": "pnpm changeset", "version": "pnpm changeset version", "publish": "pnpm publish -r", "docs": "typedoc"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "@types/jest": "^29.5.12", "@vitest/coverage-v8": "3.2.2", "jest": "30.0.0-alpha.6", "ts-jest": "^29.2.5", "typedoc": "^0.26.6", "typedoc-material-theme": "^1.1.0", "typedoc-plugin-extras": "^3.1.0", "typedoc-plugin-ga": "^1.0.5", "typescript": "^5.4.5", "vitest": "^3.2.2"}, "pnpm": {"patchedDependencies": {"bs58check@4.0.0": "patches/<EMAIL>"}, "onlyBuiltDependencies": ["@nestjs/core", "core-js", "core-js-pure", "secp256k1"]}, "packageManager": "pnpm@10.8.1"}